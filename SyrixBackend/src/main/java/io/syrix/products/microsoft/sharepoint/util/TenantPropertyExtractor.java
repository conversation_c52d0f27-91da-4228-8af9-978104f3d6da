package io.syrix.products.microsoft.sharepoint.util;

import java.util.Map;

/**
 * SharePoint-specific utility class for extracting properties from tenant objects with additional properties
 * Provides type-safe extraction with default values for SharePoint tenant configurations
 */
public class TenantPropertyExtractor {

    /**
     * Extracts a boolean property from additional properties map
     * @param additionalProperties the additional properties map
     * @param propertyName the property name to extract
     * @param defaultValue the default value if property is not found or not a boolean
     * @return the boolean value or default
     */
    public static Boolean extractBooleanProperty(Map<String, Object> additionalProperties, String propertyName, Boolean defaultValue) {
        Object value = additionalProperties.get(propertyName);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    /**
     * Extracts a string property from additional properties map
     * @param additionalProperties the additional properties map
     * @param propertyName the property name to extract
     * @param defaultValue the default value if property is not found or not a string
     * @return the string value or default
     */
    public static String extractStringProperty(Map<String, Object> additionalProperties, String propertyName, String defaultValue) {
        Object value = additionalProperties.get(propertyName);
        if (value instanceof String) {
            return (String) value;
        }
        return defaultValue;
    }

    /**
     * Extracts an integer property from additional properties map
     * @param additionalProperties the additional properties map
     * @param propertyName the property name to extract
     * @param defaultValue the default value if property is not found or not an integer
     * @return the integer value or default
     */
    public static Integer extractIntegerProperty(Map<String, Object> additionalProperties, String propertyName, Integer defaultValue) {
        Object value = additionalProperties.get(propertyName);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
}