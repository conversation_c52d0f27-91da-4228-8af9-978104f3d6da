package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.products.microsoft.sharepoint.SharepointConstants;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SharePointMalwareProtectionRemediator
 * Tests CIS 7.3.1: Ensure Office 365 SharePoint infected files are disallowed for download
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SharePoint Malware Protection Remediator Tests")
class SharePointMalwareProtectionRemediatorTest {

    @Mock
    private PowerShellSharepointClient mockClient;

    @Mock
    private SharePointTenantProperties mockTenant;

    @Mock
    private SharepointRemediationConfig mockConfig;

    @Mock
    private GeneralResult mockGeneralResult;

    @Captor
    private ArgumentCaptor<SPShellCommand<GeneralResult>> commandCaptor;

    private SharePointMalwareProtectionRemediator remediator;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        remediator = new SharePointMalwareProtectionRemediator(mockClient, mockTenant, mockConfig);
    }

    @Nested
    @DisplayName("Remediation Tests")
    class RemediationTests {

        @Test
        @DisplayName("Should succeed when malware protection is already enabled")
        void shouldSucceedWhenAlreadyEnabled() {
            // Given
            mockConfig.disallowInfectedFileDownload = true;
            mockTenant.disallowInfectedFileDownload = true;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.SUCCESS, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals(SharepointConstants.MALWARE_PROTECTION_SUCCESS, changeResult.getMessage());
        }

        @Test
        @DisplayName("Should fail when configuration is missing")
        void shouldFailWhenConfigurationMissing() {
            // Given
            remediator = new SharePointMalwareProtectionRemediator(mockClient, mockTenant, null);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("SharePoint remediation configuration is missing", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should fail when disallowInfectedFileDownload is disabled in configuration")
        void shouldFailWhenDisabledInConfiguration() {
            // Given
            mockConfig.disallowInfectedFileDownload = false;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("DisallowInfectedFileDownload is disabled in configuration", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should fail when tenant properties are not available")
        void shouldFailWhenTenantPropertiesNotAvailable() {
            // Given
            mockConfig.disallowInfectedFileDownload = true;
            remediator = new SharePointMalwareProtectionRemediator(mockClient, null, mockConfig);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("SharePoint tenant properties are not available", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should successfully enable malware protection when disabled")
        void shouldSuccessfullyEnableMalwareProtection() {
            // Given
            mockConfig.disallowInfectedFileDownload = true;
            mockTenant.disallowInfectedFileDownload = false;
            mockTenant.objectIdentity = "test-tenant-id";

            PolicyChangeResult successResult = new PolicyChangeResult();
            successResult.setStatus(RemediationResult.SUCCESS);
            successResult.setPolicyId("MS.SHAREPOINT.5.1v1");
            successResult.setMessage(SharepointConstants.MALWARE_PROTECTION_SUCCESS);
            
            ParameterChangeResult paramChange = new ParameterChangeResult();
            paramChange.setParameterName(SharepointConstants.DISALLOW_INFECTED_FILE_DOWNLOAD_PROPERTY);
            paramChange.setPrevValue(false);
            paramChange.setNewValue(true);
            paramChange.setStatus(ParameterChangeStatus.CHANGED);
            successResult.setChanges(Collections.singletonList(paramChange));

            when(mockClient.execute_(any())).thenReturn(CompletableFuture.completedFuture(mockGeneralResult));
            when(mockGeneralResult.isSuccess()).thenReturn(true);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertFalse(result.isCompletedExceptionally());
            verify(mockClient).execute_(commandCaptor.capture());
            
            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertNotNull(capturedCommand);
        }

        @Test
        @DisplayName("Should handle execution exception gracefully")
        void shouldHandleExecutionExceptionGracefully() {
            // Given
            mockConfig.disallowInfectedFileDownload = true;
            mockTenant.disallowInfectedFileDownload = false;
            mockTenant.objectIdentity = "test-tenant-id";

            RuntimeException testException = new RuntimeException("Test exception");
            when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(testException));

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertTrue(changeResult.getMessage().contains("Failed to enable malware protection"));
        }
    }

    @Nested
    @DisplayName("Rollback Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should fail rollback when fix result is null")
        void shouldFailRollbackWhenFixResultIsNull() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(null);

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("No fix result provided for rollback", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should fail rollback when no changes to rollback")
        void shouldFailRollbackWhenNoChanges() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            fixResult.setChanges(Collections.emptyList());

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("No changes to rollback", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should fail rollback when tenant properties not available")
        void shouldFailRollbackWhenTenantNotAvailable() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, null);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult();
            paramChange.setPrevValue(false);
            paramChange.setNewValue(true);
            fixResult.setChanges(Collections.singletonList(paramChange));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            assertTrue(result.isDone());
            PolicyChangeResult changeResult = result.join();
            assertEquals(RemediationResult.FAILED, changeResult.getStatus());
            assertEquals("MS.SHAREPOINT.5.1v1", changeResult.getPolicyId());
            assertEquals("SharePoint tenant properties not available for rollback", changeResult.getMessage());
        }

        @Test
        @DisplayName("Should successfully rollback changes")
        void shouldSuccessfullyRollbackChanges() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);
            
            mockTenant.objectIdentity = "test-tenant-id";
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult();
            paramChange.setPrevValue(false);  // Previous value was false
            paramChange.setNewValue(true);    // New value was true
            fixResult.setChanges(Collections.singletonList(paramChange));

            PolicyChangeResult successResult = new PolicyChangeResult();
            successResult.setStatus(RemediationResult.SUCCESS);
            successResult.setPolicyId("MS.SHAREPOINT.5.1v1");

            when(mockClient.execute_(any())).thenReturn(CompletableFuture.completedFuture(mockGeneralResult));
            when(mockGeneralResult.isSuccess()).thenReturn(true);

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            assertFalse(result.isCompletedExceptionally());
            verify(mockClient).execute_(commandCaptor.capture());
            
            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertNotNull(capturedCommand);
        }
    }

    @Nested
    @DisplayName("JSON Remediation Tests")
    class JsonRemediationTests {

        @Test
        @DisplayName("Should return JSON representation of remediation result")
        void shouldReturnJsonRepresentation() {
            // Given
            mockConfig.disallowInfectedFileDownload = true;
            mockTenant.disallowInfectedFileDownload = true;

            // When
            CompletableFuture<JsonNode> result = remediator.remediate();

            // Then
            assertTrue(result.isDone());
            JsonNode jsonResult = result.join();
            assertNotNull(jsonResult);
            assertEquals("SUCCESS", jsonResult.get("status").asText());
            assertEquals("MS.SHAREPOINT.5.1v1", jsonResult.get("policyId").asText());
        }
    }

    @Nested
    @DisplayName("Policy ID Tests")
    class PolicyIdTests {

        @Test
        @DisplayName("Should return correct policy ID")
        void shouldReturnCorrectPolicyId() {
            // When
            String policyId = remediator.getPolicyId();

            // Then
            assertEquals("MS.SHAREPOINT.5.1v1", policyId);
        }
    }
}